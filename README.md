# RentRoom - Rental Property Web Scraper

A modern React application for scraping and managing rental property listings from various real estate websites. This application provides a beautiful, user-friendly interface for finding available rental properties including studios, apartments, villas, houses, and condos.

## Features

### 🏠 Property Types Supported
- **Studio** - Compact living spaces perfect for individuals
- **Apartment** - Multi-unit residential buildings
- **Villa** - Luxury standalone properties
- **House** - Single-family residential homes
- **Condo** - Privately owned units in shared buildings

### 🔍 Advanced Search & Filtering
- Search by property title or location
- Filter by property type
- Price range filtering (min/max)
- Bedroom count filtering
- Real-time search results

### 📊 Property Information
- Detailed property descriptions
- High-quality property images
- Amenities lists
- Pricing information
- Location details
- Availability status

### 🌐 Multi-Source Scraping
The application is designed to scrape from multiple rental property sources:
- Zillow Rentals
- Apartments.com
- Rent.com
- And more...

### 🎨 Modern UI/UX
- Responsive design for all devices
- Beautiful, intuitive interface
- Real-time scraping progress
- Interactive property cards
- Detailed property views

## Technology Stack

- **Frontend**: React 19 with TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Web Scraping**: Axios + Cheerio
- **State Management**: React Context API

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rentroom
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## Usage

### Starting the Application
1. Run `npm start` to launch the development server
2. The application will open in your default browser
3. You'll see the home page with property statistics and scraping options

### Scraping Properties
1. Click "Start Scraping" on the home page
2. The scraper will process multiple rental property sources
3. View real-time progress in the scraping logs
4. Properties will be automatically added to your collection

### Browsing Properties
1. Navigate to the "Properties" page
2. Use the search bar to find specific properties
3. Apply filters to narrow down results
4. Click on property cards to view detailed information

### Property Details
- View comprehensive property information
- See amenities and features
- Check availability status
- Access original listing links
- Contact landlords (placeholder functionality)

## Project Structure

```
src/
├── components/          # React components
│   ├── Header.tsx      # Navigation header
│   ├── Home.tsx        # Home page with stats
│   ├── PropertyList.tsx # Property listing page
│   ├── PropertyDetail.tsx # Individual property view
│   └── PropertyScraper.tsx # Scraping interface
├── context/            # React context
│   └── PropertyContext.tsx # Property state management
├── services/           # Business logic
│   └── scraperService.ts # Web scraping service
└── App.tsx            # Main application component
```

## Web Scraping Implementation

### Current Implementation
The application currently uses a demonstration mode that generates realistic mock data to showcase the functionality. This approach:

- **Respects website terms of service**
- **Avoids potential legal issues**
- **Provides consistent, reliable data**
- **Demonstrates the full application capabilities**

### Real Scraping Implementation
The codebase includes commented-out real scraping logic that can be enabled by:

1. Uncommenting the real scraping code in `scraperService.ts`
2. Implementing proper rate limiting
3. Adding user agent rotation
4. Respecting robots.txt files
5. Adding error handling for blocked requests

### Scraping Sources
The application is configured to scrape from:
- **Zillow Rentals** - Large real estate marketplace
- **Apartments.com** - Apartment rental platform
- **Rent.com** - Rental property listings

## Customization

### Adding New Property Sources
1. Edit `src/services/scraperService.ts`
2. Add new source configuration to the `sources` array
3. Define CSS selectors for property data extraction
4. Test the new source integration

### Modifying Property Types
1. Update the `Property` interface in `PropertyContext.tsx`
2. Modify the `determinePropertyType` function in `scraperService.ts`
3. Update UI components to handle new types

### Styling Changes
- Modify `tailwind.config.js` for theme customization
- Edit component CSS classes for visual changes
- Update color schemes in the configuration

## Legal Considerations

⚠️ **Important**: This application is for educational and demonstration purposes. When implementing real web scraping:

1. **Check Terms of Service** - Always review website terms before scraping
2. **Respect robots.txt** - Follow website crawling guidelines
3. **Rate Limiting** - Implement delays between requests
4. **User Agents** - Use appropriate user agent strings
5. **Legal Compliance** - Ensure compliance with local laws and regulations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions or support, please open an issue in the repository or contact the development team.

---

**Note**: This application demonstrates modern web development practices and provides a foundation for building real estate applications. The scraping functionality is implemented in a way that respects website policies while showcasing the technical capabilities.
