# UAE Property Data Generation Guide

This guide explains how to use the manual data generation approach for loading realistic UAE property data into the RentRoom application.

## Overview

Instead of browser-based scraping (which has CORS limitations), this application uses a **manual data generation approach** that creates realistic UAE property data and loads it from a JSON file.

## Quick Start

### 1. Generate Property Data
```bash
npm run generate-data
```

### 2. Start the Application
```bash
npm start
```

The application will automatically load the generated property data on startup.

## Data Generation Details

### What Gets Generated

The `npm run generate-data` command creates:

- **134+ realistic UAE properties**
- **Dubai locations**: Marina, Downtown, JBR, Business Bay, DIFC, etc.
- **Abu Dhabi locations**: Corniche, Al Reem Island, Yas Island, etc.
- **Property types**: Studios, Apartments, Villas, Houses
- **Market-accurate pricing**: AED 21k - 350k based on location and type
- **Realistic amenities**: Swimming pools, gyms, parking, security, etc.

### Sample Output
```
🚀 Starting UAE property data generation...
🏠 Generating realistic UAE property data...
✅ Generated 134 realistic UAE properties
💾 Saved 134 properties to src/data/properties.json

📊 Summary:
Total Properties: 134
By Type: { studio: 26, house: 20, apartment: 51, villa: 37 }
By Location: { Dubai: 94, Abu Dhabi: 40 }
Price Range: AED 21,024 - 350,457
Average Price: AED 111,669
```

## Generated Data Structure

Each property includes:

```json
{
  "id": "dubai-dubai-marina-1754373617332-0",
  "title": "Studio in Dubai Marina",
  "type": "studio",
  "price": 57346,
  "location": "Dubai Marina, Dubai, UAE",
  "bedrooms": 0,
  "bathrooms": 1,
  "area": 366,
  "description": "Beautiful studio available for rent in Dubai Marina...",
  "imageUrl": "https://picsum.photos/400/300?random=...",
  "amenities": ["Swimming Pool", "Gym", "Parking", "Security"],
  "available": true,
  "source": "Property Finder UAE",
  "url": "https://www.propertyfinder.ae/property-...",
  "scrapedAt": "2024-01-01T00:00:00.000Z"
}
```

## Pricing Logic

### Base Prices (AED per year)
- **Studio**: 35,000
- **1-Bed Apartment**: 55,000  
- **2-Bed Apartment**: 75,000
- **3-Bed Apartment**: 95,000
- **Villa**: 150,000+
- **House**: 120,000+

### Location Multipliers
- **Dubai Marina**: 1.5x
- **Downtown Dubai**: 1.6x
- **JBR**: 1.4x
- **Business Bay**: 1.3x
- **DIFC**: 1.7x
- **Al Reem Island**: 1.2x
- **Corniche**: 1.3x

## Customization

### Modify Locations
Edit `scripts/scrape-properties.js`:

```javascript
const dubaiLocations = [
  { name: 'Your New Area', priceMultiplier: 1.2 },
  // Add more locations
];
```

### Adjust Pricing
```javascript
const propertyTypes = [
  { type: 'studio', basePrice: 40000, ... }, // Increase base price
  // Modify other types
];
```

### Change Property Distribution
```javascript
// Modify the number of properties per location
const numProperties = Math.floor(Math.random() * 10) + 5; // 5-15 properties
```

## File Locations

- **Generator Script**: `scripts/scrape-properties.js`
- **Generated Data**: `src/data/properties.json`
- **Data Loader**: `src/services/scraperService.ts`

## Advantages

### vs Browser Scraping:
1. **No CORS Issues** - Works in any environment
2. **Reliable Data** - Always loads successfully  
3. **Fast Performance** - No network requests
4. **Realistic Content** - Market-accurate UAE data
5. **Easy Customization** - Modify generation script

### vs Real Scraping:
1. **No Legal Issues** - No website terms violations
2. **No Rate Limiting** - No server overload concerns
3. **Consistent Results** - Same data every time
4. **No Blocking** - No IP bans or CAPTCHAs

## Regenerating Data

To create fresh property data:

```bash
# Generate new data
npm run generate-data

# Restart the application to load new data
npm start
```

## Integration with Application

The application automatically:

1. **Loads data** from `src/data/properties.json` on startup
2. **Groups by source** for display purposes
3. **Converts dates** from JSON strings to Date objects
4. **Validates types** to ensure TypeScript compatibility

## Troubleshooting

### Data Not Loading
- Ensure `src/data/properties.json` exists
- Check file permissions
- Verify JSON format is valid

### Build Errors
- Run `npm run generate-data` before building
- Ensure TypeScript types match generated data

### Performance Issues
- Reduce number of generated properties in script
- Optimize image loading with lazy loading

## Production Deployment

For production:

1. **Generate data**: `npm run generate-data`
2. **Build application**: `npm run build`
3. **Deploy**: Upload build folder to hosting service

The generated data file will be included in the build automatically.

---

**Note**: This approach provides realistic demonstration data. For production use with live property data, consider implementing server-side scraping or using official real estate APIs.
