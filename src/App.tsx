import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Home from './components/Home';
import PropertyList from './components/PropertyList';
import PropertyDetail from './components/PropertyDetail';
import { PropertyProvider } from './context/PropertyContext';
import './App.css';

function App() {
  return (
    <PropertyProvider>
      <Router>
        <div className="App min-h-screen bg-gray-50">
          <Header />
          <main className="container mx-auto px-4 py-8">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/properties" element={<PropertyList />} />
              <Route path="/property/:id" element={<PropertyDetail />} />
            </Routes>
          </main>
        </div>
      </Router>
    </PropertyProvider>
  );
}

export default App;
