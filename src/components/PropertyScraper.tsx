import React, { useState } from 'react';
import { X, Search, Loader2, AlertCircle } from 'lucide-react';
import { useProperty } from '../context/PropertyContext';

import scraperService, { ScrapingResult } from '../services/scraperService';

interface PropertyScraperProps {
  onClose: () => void;
}

const PropertyScraper: React.FC<PropertyScraperProps> = ({ onClose }) => {
  const { dispatch } = useProperty();
  const [isScraping, setIsScraping] = useState(false);
  const [scrapedCount, setScrapedCount] = useState(0);
  const [currentSource, setCurrentSource] = useState('');
  const [logs, setLogs] = useState<string[]>([]);

  const sources = scraperService.getAvailableSources();

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const scrapeSource = async (source: any) => {
    setCurrentSource(source.name);
    addLog(`Starting to scrape ${source.name}...`);
    
    try {
      const result: ScrapingResult = await scraperService.scrapeSingleSource(source.name);
      
      if (result.success) {
        result.properties.forEach(property => {
          dispatch({ type: 'ADD_PROPERTY', payload: property });
          setScrapedCount(prev => prev + 1);
        });
        
        addLog(`✅ Successfully scraped ${result.properties.length} properties from ${source.name}`);
      } else {
        addLog(`❌ Failed to scrape ${source.name}: ${result.error}`);
      }
    } catch (error) {
      addLog(`❌ Error scraping ${source.name}: ${error}`);
    }
  };

  const startScraping = async () => {
    setIsScraping(true);
    setScrapedCount(0);
    setLogs([]);
    addLog('🚀 Starting UAE rental property scraping...');
    
    try {
      for (const source of sources) {
        if (!isScraping) break; // Check if scraping was stopped
        await scrapeSource(source);
      }
      
      addLog('🎉 Scraping completed successfully!');
    } catch (error) {
      addLog(`❌ Error during scraping: ${error}`);
    } finally {
      setIsScraping(false);
      setCurrentSource('');
    }
  };

  const stopScraping = () => {
    setIsScraping(false);
    addLog('⏹️ Scraping stopped by user');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-semibold text-gray-900">UAE Rental Property Scraper</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">UAE Property Sources</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sources.map((source) => (
                <div
                  key={source.name}
                  className="flex items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{source.name}</div>
                    <div className="text-sm text-gray-500">{source.url}</div>
                  </div>
                  {currentSource === source.name && isScraping && (
                    <Loader2 className="w-5 h-5 text-primary-600 animate-spin" />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Scraping Status</h3>
              <div className="flex items-center space-x-4">
                {isScraping && (
                  <div className="flex items-center text-primary-600">
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    <span>Scraping...</span>
                  </div>
                )}
                <div className="text-sm text-gray-600">
                  Properties found: {scrapedCount}
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              {!isScraping ? (
                <button
                  onClick={startScraping}
                  className="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center space-x-2"
                >
                  <Search className="w-4 h-4" />
                  <span>Start Scraping</span>
                </button>
              ) : (
                <button
                  onClick={stopScraping}
                  className="bg-red-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors"
                >
                  Stop Scraping
                </button>
              )}
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Scraping Logs</h3>
            <div className="bg-gray-100 rounded-lg p-4 h-64 overflow-y-auto logs-container">
              {logs.length === 0 ? (
                <div className="text-gray-500 text-center py-8">
                  No logs yet. Start scraping to see progress.
                </div>
              ) : (
                <div className="space-y-2">
                  {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono">
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">UAE Property Scraping</h4>
                <p className="text-sm text-blue-700">
                  This scraper is configured for UAE property websites (Dubizzle, Bayut, Property Finder, Just Property). 
                  Currently using demo data with realistic UAE property information. To enable real scraping, 
                  uncomment the scraping logic in scraperService.ts and ensure compliance with website terms of service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyScraper; 