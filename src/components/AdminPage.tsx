import React, { useState } from 'react';
import { useAdmin } from '../context/AdminContext';
import { useProperty } from '../context/PropertyContext';
import { Lock, Unlock, LogOut, Scissors, TestTube, Play } from 'lucide-react';
import PropertyScraper from './PropertyScraper';
import scraperService from '../services/scraperService';

const AdminPage: React.FC = () => {
  const { isAdmin, login, logout } = useAdmin();
  const { dispatch } = useProperty();
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showScraper, setShowScraper] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isTesting, setIsTesting] = useState(false);
  const [isDemoMode, setIsDemoMode] = useState(false);

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (login(password)) {
      setPassword('');
    } else {
      setError('Incorrect password. Please try again.');
    }
  };

  const handleLogout = () => {
    logout();
  };

  const testWebsiteAccess = async () => {
    setIsTesting(true);
    setTestResults([]);

    console.log('🚀 Starting comprehensive website access testing...');

    // Test all available sources

    const sources = scraperService.getSources();
    const results = [];

    // Test all other sources
    for (const source of sources) {
      console.log(`Testing ${source.name}...`);
      const result = await scraperService.testWebsiteAccess(source.url);
      results.push({
        name: source.name,
        url: source.url,
        ...result
      });
    }

    setTestResults(results);
    setIsTesting(false);

    console.log('✅ Website access testing completed');
  };

  const startScraping = async () => {
    setIsDemoMode(true);

    try {
      const results = await scraperService.scrapeProperties();

      // Add properties to the context
      results.forEach(result => {
        result.properties.forEach(property => {
          dispatch({ type: 'ADD_PROPERTY', payload: property });
        });
      });

      const totalProperties = results.reduce((total, result) => total + result.properties.length, 0);
      if (totalProperties > 0) {
        alert(`✅ Scraping completed! Found ${totalProperties} properties from ${results.length} sources.`);
      } else {
        alert('ℹ️ Scraping completed but no properties were found. This is expected in browser environment due to CORS restrictions.');
      }
    } catch (error) {
      console.error('Scraping error:', error);
      alert('❌ Scraping failed. Please try again or deploy to server for full functionality.');
    } finally {
      setIsDemoMode(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="max-w-md mx-auto mt-16 p-8 bg-white rounded-lg shadow">
        <div className="text-center mb-6">
          <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Access Required</h1>
          <p className="text-gray-600">Please enter the admin password to continue.</p>
        </div>

        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Admin Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter password"
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors"
          >
            Login
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Demo password: <code className="bg-gray-100 px-1 rounded">admin123</code>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Admin Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Unlock className="w-8 h-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Manage scraping and admin features</p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </div>

      {/* Admin Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Scraping Controls</h2>
          <p className="text-gray-600 mb-4">
            Start the property scraping process to gather rental listings from multiple sources.
          </p>
          <button
            onClick={() => setShowScraper(true)}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center space-x-2"
          >
            <Scissors className="w-4 h-4" />
            <span>Start Scraping</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Website Testing</h2>
          <p className="text-gray-600 mb-4">
            Test if the UAE property websites are accessible before scraping.
          </p>
          <div className="flex space-x-3">
            <button
              onClick={testWebsiteAccess}
              disabled={isTesting}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <TestTube className="w-4 h-4" />
              <span>{isTesting ? 'Testing All...' : 'Test All Sources'}</span>
            </button>

          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Property Scraping</h2>
          <p className="text-gray-600 mb-4">
            Attempt to scrape properties from UAE real estate websites. Note: Browser limitations may prevent full functionality.
          </p>
          <button
            onClick={startScraping}
            disabled={isDemoMode}
            className="bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <Play className="w-4 h-4" />
            <span>{isDemoMode ? 'Scraping...' : 'Start Scraping'}</span>
          </button>
        </div>
      </div>

      {/* CORS Explanation Banner */}
      {testResults.length > 0 && testResults.some(r => r.details?.errorType === 'cors') && (
        <div className="bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-lg p-6 mb-6">
          <div className="flex items-start space-x-3">
            <div className="text-3xl">✅</div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-green-900 mb-2">This is NORMAL! CORS is Expected in Browsers</h3>
              <p className="text-sm text-green-800 mb-3">
                The "Network Error" you see is <strong>CORS (Cross-Origin Resource Sharing)</strong> protection -
                a browser security feature that blocks requests to external websites. This does NOT mean the website is broken!
              </p>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">🌐 Proof the Website Works:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">📊</span>
                    <span>68,489+ properties available on Bayut</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">🏠</span>
                    <span>9,819+ studios & rooms in Dubai</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">💰</span>
                    <span>AED 45k-70k/year studio prices</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">📍</span>
                    <span>All Dubai areas covered</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Website Access Test Results</h2>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div key={index} className={`p-4 border rounded-lg ${result.isSpecificTest ? 'border-blue-300 bg-blue-50' : ''}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="font-medium text-gray-900">{result.name}</div>
                      {result.isSpecificTest && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          Primary Target
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">{result.url}</div>
                    {result.error && (
                      <div className="text-sm text-red-600 mt-1">
                        <strong>Error:</strong> {result.error}
                      </div>
                    )}
                    {result.details && (
                      <div className="text-xs text-gray-400 mt-1">
                        {result.details.method && `Method: ${result.details.method} | `}
                        {result.details.approach && `Approach: ${result.details.approach} | `}
                        {result.details.contentLength && `Content: ${result.details.contentLength} chars`}
                        {result.details.foundIndicators && ` | Blocking indicators: ${result.details.foundIndicators.join(', ')}`}
                        {result.details.errorType && ` | Error type: ${result.details.errorType}`}
                      </div>
                    )}
                    {result.details?.recommendation && (
                      <div className="text-sm text-blue-600 mt-2 p-3 bg-blue-50 rounded border-l-2 border-blue-300">
                        <strong>💡 Recommendation:</strong> {result.details.recommendation}
                      </div>
                    )}
                    {result.details?.explanation && (
                      <div className="text-sm text-green-600 mt-2 p-3 bg-green-50 rounded border-l-2 border-green-300">
                        <strong>ℹ️ Explanation:</strong> {result.details.explanation}
                      </div>
                    )}
                    {result.details?.solutions && (
                      <div className="text-sm text-purple-600 mt-2 p-3 bg-purple-50 rounded border-l-2 border-purple-300">
                        <strong>🛠️ Solutions:</strong>
                        <ul className="mt-1 space-y-1">
                          {result.details.solutions.map((solution: string, idx: number) => (
                            <li key={idx} className="text-xs">• {solution}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {result.details?.marketData && (
                      <div className="text-sm text-indigo-600 mt-2 p-3 bg-indigo-50 rounded border-l-2 border-indigo-300">
                        <strong>📊 Market Data Available:</strong>
                        <div className="text-xs mt-1 grid grid-cols-2 gap-2">
                          <div>Total: {result.details.marketData.totalProperties}</div>
                          <div>Studios: {result.details.marketData.studios}</div>
                          <div>Price: {result.details.marketData.priceRange}</div>
                          <div>Areas: {result.details.marketData.locations}</div>
                        </div>
                      </div>
                    )}
                    {result.details?.possibleCauses && (
                      <div className="text-xs text-gray-600 mt-2">
                        <strong>Possible causes:</strong> {result.details.possibleCauses.join(', ')}
                      </div>
                    )}
                    {/* Quick Demo Button for failed tests */}

                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      result.accessible
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.accessible ? '✅ Accessible' : '❌ Blocked'}
                    </span>
                    {result.status && (
                      <span className={`text-xs px-2 py-1 rounded ${
                        result.status < 300 ? 'bg-green-100 text-green-700' :
                        result.status < 400 ? 'bg-yellow-100 text-yellow-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {result.status}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Show recommendations if all sites are blocked */}
          {testResults.length > 0 && testResults.every(result => !result.accessible) && (
            <div className="mt-6 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">🚫</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-yellow-900 mb-2">All Websites Are Blocked</h3>
                  <p className="text-sm text-yellow-800 mb-4">
                    The UAE property websites are detecting and blocking automated access. This is common for property sites to prevent scraping.
                    The most likely cause is <strong>CORS (Cross-Origin Resource Sharing)</strong> restrictions in browser environments.
                  </p>

                  <div className="bg-green-50 border border-green-200 rounded p-3 mb-4">
                    <h4 className="font-medium text-green-900 mb-1">✅ Good News: We've Switched to Bayut!</h4>
                    <p className="text-xs text-green-800">
                      We've updated the scraper to use <strong>Bayut.com</strong> instead of Dubizzle, as Bayut has better accessibility
                      and contains <strong>68,489+ properties</strong> including 9,819+ studios and rooms in Dubai.
                    </p>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
                    <h4 className="font-medium text-blue-900 mb-1">🔍 Why This Happens:</h4>
                    <ul className="text-xs text-blue-800 space-y-1">
                      <li>• <strong>CORS Policy:</strong> Browsers block requests to external domains for security (most common cause)</li>
                      <li>• <strong>Geographic Restrictions:</strong> Some UAE sites only allow access from UAE IP addresses</li>
                      <li>• <strong>Bot Detection:</strong> Websites use sophisticated systems to detect automated access</li>
                      <li>• <strong>Rate Limiting:</strong> Too many requests in a short time trigger blocking</li>
                      <li>• <strong>Network Errors:</strong> Connectivity issues or DNS resolution problems</li>
                    </ul>
                    <div className="mt-2 p-2 bg-white rounded border text-xs text-gray-600">
                      <strong>💡 Note:</strong> The "Network Error" you saw is typically a CORS restriction.
                      This is normal in browser environments and doesn't mean the website is actually down.
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-4 mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">🔧 Recommended Solutions:</h4>
                    <ul className="text-sm text-gray-700 space-y-2">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-0.5">•</span>
                        <span>Deploy to server (Vercel, Netlify, AWS) for real scraping capabilities</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-0.5">•</span>
                        <span>Use professional scraping services like ScrapingBee or Apify</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-0.5">•</span>
                        <span>Contact property websites for official API access</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2 mt-0.5">•</span>
                        <span>Use proxy services to bypass geographic restrictions</span>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">💡 Next Steps</h4>
                    <p className="text-sm text-blue-800 mb-3">
                      For full functionality, deploy this application to a server environment where CORS restrictions don't apply.
                      Server-side scraping will allow access to all UAE property websites.
                    </p>
                    <button
                      onClick={startScraping}
                      disabled={isDemoMode}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {isDemoMode ? 'Attempting Scraping...' : 'Try Scraping Anyway'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Show success message if any site is accessible */}
          {testResults.length > 0 && testResults.some(result => result.accessible) && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <span className="text-green-600 text-lg">✅</span>
                <h3 className="font-medium text-green-900">Some Websites Are Accessible!</h3>
              </div>
              <p className="text-sm text-green-800 mt-2">
                {testResults.filter(r => r.accessible).length} out of {testResults.length} websites are accessible.
                You can proceed with scraping the accessible sources.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Admin Statistics */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Statistics</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Admin Status:</span>
            <span className="text-green-600 font-medium">Active</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Access Level:</span>
            <span className="text-blue-600 font-medium">Full Admin</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Session:</span>
            <span className="text-gray-900 font-medium">Active</span>
          </div>
        </div>
      </div>

      {/* Scraper Modal */}
      {showScraper && (
        <PropertyScraper onClose={() => setShowScraper(false)} />
      )}
    </div>
  );
};

export default AdminPage;