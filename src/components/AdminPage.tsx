import React, { useState } from 'react';
import { useAdmin } from '../context/AdminContext';
import { useProperty } from '../context/PropertyContext';
import { Lock, Unlock, LogOut, Scissors, TestTube, Play } from 'lucide-react';
import PropertyScraper from './PropertyScraper';
import scraperService from '../services/scraperService';

const AdminPage: React.FC = () => {
  const { isAdmin, login, logout } = useAdmin();
  const { dispatch } = useProperty();
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showScraper, setShowScraper] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isTesting, setIsTesting] = useState(false);
  const [isDemoMode, setIsDemoMode] = useState(false);

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (login(password)) {
      setPassword('');
    } else {
      setError('Incorrect password. Please try again.');
    }
  };

  const handleLogout = () => {
    logout();
  };

  const testWebsiteAccess = async () => {
    setIsTesting(true);
    setTestResults([]);

    console.log('🚀 Starting comprehensive website access testing...');

    // First test the specific Dubizzle rooms URL
    console.log('Testing Dubizzle Rooms for Rent page specifically...');
    const dubizzleResult = await scraperService.testDubizzleRoomsAccess();

    const sources = scraperService.getAvailableSources();
    const results = [];

    // Add the specific Dubizzle test result
    results.push({
      name: 'Dubizzle Rooms (Specific Test)',
      url: 'https://dubai.dubizzle.com/en/property-for-rent/rooms-for-rent-flatmates',
      ...dubizzleResult,
      isSpecificTest: true
    });

    // Test all other sources
    for (const source of sources) {
      console.log(`Testing ${source.name}...`);
      const result = await scraperService.testWebsiteAccess(source.url);
      results.push({
        name: source.name,
        url: source.url,
        ...result
      });
    }

    setTestResults(results);
    setIsTesting(false);

    console.log('✅ Website access testing completed');
  };

  const startDemoMode = async () => {
    setIsDemoMode(true);
    
    try {
      const results = await scraperService.simulateSuccessfulScraping();
      
      // Add properties to the context
      results.forEach(result => {
        result.properties.forEach(property => {
          dispatch({ type: 'ADD_PROPERTY', payload: property });
        });
      });
      
      console.log('Demo mode completed successfully!');
    } catch (error) {
      console.error('Demo mode error:', error);
    } finally {
      setIsDemoMode(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="max-w-md mx-auto mt-16 p-8 bg-white rounded-lg shadow">
        <div className="text-center mb-6">
          <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Access Required</h1>
          <p className="text-gray-600">Please enter the admin password to continue.</p>
        </div>

        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Admin Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter password"
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors"
          >
            Login
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Demo password: <code className="bg-gray-100 px-1 rounded">admin123</code>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Admin Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Unlock className="w-8 h-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Manage scraping and admin features</p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </div>

      {/* Admin Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Scraping Controls</h2>
          <p className="text-gray-600 mb-4">
            Start the property scraping process to gather rental listings from multiple sources.
          </p>
          <button
            onClick={() => setShowScraper(true)}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center space-x-2"
          >
            <Scissors className="w-4 h-4" />
            <span>Start Scraping</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Website Testing</h2>
          <p className="text-gray-600 mb-4">
            Test if the UAE property websites are accessible before scraping.
          </p>
          <div className="flex space-x-3">
            <button
              onClick={testWebsiteAccess}
              disabled={isTesting}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <TestTube className="w-4 h-4" />
              <span>{isTesting ? 'Testing All...' : 'Test All Sources'}</span>
            </button>
            <button
              onClick={async () => {
                setIsTesting(true);
                setTestResults([]);
                console.log('🚀 Starting Dubizzle Rooms quick test...');

                const result = await scraperService.testDubizzleRoomsAccess();

                setTestResults([{
                  name: 'Dubizzle Rooms (Quick Test)',
                  url: 'https://dubai.dubizzle.com/en/property-for-rent/rooms-for-rent-flatmates',
                  ...result,
                  isSpecificTest: true
                }]);

                // If the test failed, automatically suggest demo data
                if (!result.accessible) {
                  console.log('❌ Test failed, offering demo data option...');
                  console.log('💡 Recommendation: Use the "Generate Demo Rooms" button for immediate data');
                }

                setIsTesting(false);
              }}
              disabled={isTesting}
              className="bg-orange-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-orange-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <TestTube className="w-4 h-4" />
              <span>{isTesting ? 'Testing...' : 'Quick Test Dubizzle'}</span>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Demo Mode</h2>
          <p className="text-gray-600 mb-4">
            Generate realistic UAE property data when websites are blocked.
          </p>
          <button
            onClick={startDemoMode}
            disabled={isDemoMode}
            className="bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <Play className="w-4 h-4" />
            <span>{isDemoMode ? 'Generating...' : 'Start Demo'}</span>
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Website Access Test Results</h2>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div key={index} className={`p-4 border rounded-lg ${result.isSpecificTest ? 'border-blue-300 bg-blue-50' : ''}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="font-medium text-gray-900">{result.name}</div>
                      {result.isSpecificTest && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          Primary Target
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">{result.url}</div>
                    {result.error && (
                      <div className="text-sm text-red-600 mt-1">
                        <strong>Error:</strong> {result.error}
                      </div>
                    )}
                    {result.details && (
                      <div className="text-xs text-gray-400 mt-1">
                        {result.details.method && `Method: ${result.details.method} | `}
                        {result.details.approach && `Approach: ${result.details.approach} | `}
                        {result.details.contentLength && `Content: ${result.details.contentLength} chars`}
                        {result.details.foundIndicators && ` | Blocking indicators: ${result.details.foundIndicators.join(', ')}`}
                        {result.details.errorType && ` | Error type: ${result.details.errorType}`}
                      </div>
                    )}
                    {result.details?.recommendation && (
                      <div className="text-sm text-blue-600 mt-2 p-2 bg-blue-50 rounded border-l-2 border-blue-300">
                        <strong>💡 Recommendation:</strong> {result.details.recommendation}
                      </div>
                    )}
                    {result.details?.possibleCauses && (
                      <div className="text-xs text-gray-600 mt-2">
                        <strong>Possible causes:</strong> {result.details.possibleCauses.join(', ')}
                      </div>
                    )}
                    {/* Quick Demo Button for failed tests */}
                    {!result.accessible && result.isSpecificTest && (
                      <div className="mt-3 flex space-x-2">
                        <button
                          onClick={async () => {
                            console.log('🎭 Generating demo data for Dubizzle Rooms...');
                            const demoResult = scraperService.generateDubizzleRoomsDemoData();

                            // Add properties to the context
                            demoResult.properties.forEach(property => {
                              dispatch({ type: 'ADD_PROPERTY', payload: property });
                            });

                            alert(`✅ Generated ${demoResult.properties.length} demo room listings! Check the Properties page.`);
                          }}
                          className="bg-purple-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-purple-700 transition-colors"
                        >
                          🎭 Generate Demo Rooms
                        </button>
                        <button
                          onClick={() => window.open('https://dubai.dubizzle.com/en/property-for-rent/rooms-for-rent-flatmates', '_blank')}
                          className="bg-gray-600 text-white px-3 py-1 rounded text-xs font-medium hover:bg-gray-700 transition-colors"
                        >
                          🌐 Open in Browser
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      result.accessible
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.accessible ? '✅ Accessible' : '❌ Blocked'}
                    </span>
                    {result.status && (
                      <span className={`text-xs px-2 py-1 rounded ${
                        result.status < 300 ? 'bg-green-100 text-green-700' :
                        result.status < 400 ? 'bg-yellow-100 text-yellow-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {result.status}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Show recommendations if all sites are blocked */}
          {testResults.length > 0 && testResults.every(result => !result.accessible) && (
            <div className="mt-6 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">🚫</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-yellow-900 mb-2">All Websites Are Blocked</h3>
                  <p className="text-sm text-yellow-800 mb-4">
                    The UAE property websites are detecting and blocking automated access. This is common for property sites to prevent scraping.
                    The most likely cause is <strong>CORS (Cross-Origin Resource Sharing)</strong> restrictions in browser environments.
                  </p>

                  <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
                    <h4 className="font-medium text-blue-900 mb-1">🔍 Why This Happens:</h4>
                    <ul className="text-xs text-blue-800 space-y-1">
                      <li>• <strong>CORS Policy:</strong> Browsers block requests to external domains for security (most common cause)</li>
                      <li>• <strong>Geographic Restrictions:</strong> Some UAE sites only allow access from UAE IP addresses</li>
                      <li>• <strong>Bot Detection:</strong> Websites use sophisticated systems to detect automated access</li>
                      <li>• <strong>Rate Limiting:</strong> Too many requests in a short time trigger blocking</li>
                      <li>• <strong>Network Errors:</strong> Connectivity issues or DNS resolution problems</li>
                    </ul>
                    <div className="mt-2 p-2 bg-white rounded border text-xs text-gray-600">
                      <strong>💡 Note:</strong> The "Network Error" you saw is typically a CORS restriction.
                      This is normal in browser environments and doesn't mean the website is actually down.
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-4 mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">🔧 Recommended Solutions:</h4>
                    <ul className="text-sm text-gray-700 space-y-2">
                      {scraperService.getProxyRecommendations().map((recommendation, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-500 mr-2 mt-0.5">•</span>
                          <span dangerouslySetInnerHTML={{ __html: recommendation }} />
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">💡 Alternative: Use Demo Mode</h4>
                    <p className="text-sm text-blue-800 mb-3">
                      While working on bypassing the blocks, you can use our demo mode which generates realistic UAE property data
                      to test and develop the application functionality.
                    </p>
                    <button
                      onClick={startDemoMode}
                      disabled={isDemoMode}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {isDemoMode ? 'Generating Demo Data...' : 'Start Demo Mode'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Show success message if any site is accessible */}
          {testResults.length > 0 && testResults.some(result => result.accessible) && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <span className="text-green-600 text-lg">✅</span>
                <h3 className="font-medium text-green-900">Some Websites Are Accessible!</h3>
              </div>
              <p className="text-sm text-green-800 mt-2">
                {testResults.filter(r => r.accessible).length} out of {testResults.length} websites are accessible.
                You can proceed with scraping the accessible sources.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Admin Statistics */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Statistics</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Admin Status:</span>
            <span className="text-green-600 font-medium">Active</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Access Level:</span>
            <span className="text-blue-600 font-medium">Full Admin</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Session:</span>
            <span className="text-gray-900 font-medium">Active</span>
          </div>
        </div>
      </div>

      {/* Scraper Modal */}
      {showScraper && (
        <PropertyScraper onClose={() => setShowScraper(false)} />
      )}
    </div>
  );
};

export default AdminPage;