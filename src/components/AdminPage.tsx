import React, { useState } from 'react';
import { useAdmin } from '../context/AdminContext';
import { useProperty } from '../context/PropertyContext';
import { Lock, Unlock, LogOut, Scissors, TestTube, Play } from 'lucide-react';
import PropertyScraper from './PropertyScraper';
import scraperService from '../services/scraperService';

const AdminPage: React.FC = () => {
  const { isAdmin, login, logout } = useAdmin();
  const { dispatch } = useProperty();
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showScraper, setShowScraper] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isTesting, setIsTesting] = useState(false);
  const [isDemoMode, setIsDemoMode] = useState(false);

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (login(password)) {
      setPassword('');
    } else {
      setError('Incorrect password. Please try again.');
    }
  };

  const handleLogout = () => {
    logout();
  };

  const testWebsiteAccess = async () => {
    setIsTesting(true);
    setTestResults([]);
    
    const sources = scraperService.getAvailableSources();
    const results = [];
    
    for (const source of sources) {
      console.log(`Testing ${source.name}...`);
      const result = await scraperService.testWebsiteAccess(source.url);
      results.push({
        name: source.name,
        url: source.url,
        ...result
      });
    }
    
    setTestResults(results);
    setIsTesting(false);
  };

  const startDemoMode = async () => {
    setIsDemoMode(true);
    
    try {
      const results = await scraperService.simulateSuccessfulScraping();
      
      // Add properties to the context
      results.forEach(result => {
        result.properties.forEach(property => {
          dispatch({ type: 'ADD_PROPERTY', payload: property });
        });
      });
      
      console.log('Demo mode completed successfully!');
    } catch (error) {
      console.error('Demo mode error:', error);
    } finally {
      setIsDemoMode(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="max-w-md mx-auto mt-16 p-8 bg-white rounded-lg shadow">
        <div className="text-center mb-6">
          <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Access Required</h1>
          <p className="text-gray-600">Please enter the admin password to continue.</p>
        </div>

        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Admin Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter password"
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors"
          >
            Login
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Demo password: <code className="bg-gray-100 px-1 rounded">admin123</code>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Admin Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Unlock className="w-8 h-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Manage scraping and admin features</p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </div>

      {/* Admin Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Scraping Controls</h2>
          <p className="text-gray-600 mb-4">
            Start the property scraping process to gather rental listings from multiple sources.
          </p>
          <button
            onClick={() => setShowScraper(true)}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center space-x-2"
          >
            <Scissors className="w-4 h-4" />
            <span>Start Scraping</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Website Testing</h2>
          <p className="text-gray-600 mb-4">
            Test if the UAE property websites are accessible before scraping.
          </p>
          <button
            onClick={testWebsiteAccess}
            disabled={isTesting}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <TestTube className="w-4 h-4" />
            <span>{isTesting ? 'Testing...' : 'Test Access'}</span>
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Demo Mode</h2>
          <p className="text-gray-600 mb-4">
            Generate realistic UAE property data when websites are blocked.
          </p>
          <button
            onClick={startDemoMode}
            disabled={isDemoMode}
            className="bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <Play className="w-4 h-4" />
            <span>{isDemoMode ? 'Generating...' : 'Start Demo'}</span>
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Website Access Test Results</h2>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{result.name}</div>
                  <div className="text-sm text-gray-500">{result.url}</div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    result.accessible 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {result.accessible ? 'Accessible' : 'Blocked'}
                  </span>
                  {result.status && (
                    <span className="text-xs text-gray-500">Status: {result.status}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {/* Show recommendations if all sites are blocked */}
          {testResults.every(result => !result.accessible) && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">All Websites Are Blocked</h3>
              <p className="text-sm text-yellow-800 mb-3">
                The UAE property websites are blocking automated access. Here are some solutions:
              </p>
              <ul className="text-sm text-yellow-800 space-y-1">
                {scraperService.getProxyRecommendations().map((recommendation, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-600 mr-2">•</span>
                    {recommendation}
                  </li>
                ))}
              </ul>
              <div className="mt-4 p-3 bg-white rounded border">
                <p className="text-xs text-gray-600">
                  <strong>Quick Test:</strong> Try accessing these sites manually in your browser. 
                  If they work there but not here, it's likely a geographic or bot detection issue.
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Admin Statistics */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Statistics</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Admin Status:</span>
            <span className="text-green-600 font-medium">Active</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Access Level:</span>
            <span className="text-blue-600 font-medium">Full Admin</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Session:</span>
            <span className="text-gray-900 font-medium">Active</span>
          </div>
        </div>
      </div>

      {/* Scraper Modal */}
      {showScraper && (
        <PropertyScraper onClose={() => setShowScraper(false)} />
      )}
    </div>
  );
};

export default AdminPage;