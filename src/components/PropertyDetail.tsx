import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, MapPin, Bed, Bath, Square, ExternalLink, Check } from 'lucide-react';
import { useProperty } from '../context/PropertyContext';

const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { state } = useProperty();
  
  const property = state.properties.find(p => p.id === id);

  if (!property) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">🏠</div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Property not found</h3>
        <p className="text-gray-600 mb-6">
          The property you're looking for doesn't exist or has been removed.
        </p>
        <Link
          to="/properties"
          className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          Back to Properties
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Back Button */}
      <div className="mb-6">
        <Link
          to="/properties"
          className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Properties
        </Link>
      </div>

      {/* Property Header */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden mb-8">
        <div className="h-96 bg-gray-200 relative">
          {property.imageUrl ? (
            <img
              src={property.imageUrl}
              alt={property.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400 text-8xl">
              🏠
            </div>
          )}
          <div className="absolute top-4 right-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              property.available 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {property.available ? 'Available' : 'Rented'}
            </span>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {property.title}
              </h1>
              <div className="flex items-center text-gray-600 mb-4">
                <MapPin className="w-5 h-5 mr-2" />
                <span className="text-lg">{property.location}</span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-primary-600">
                ${property.price.toLocaleString()}
              </div>
              <div className="text-gray-600">per month</div>
            </div>
          </div>

          {/* Property Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Bed className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{property.bedrooms}</div>
              <div className="text-sm text-gray-600">Bedrooms</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Bath className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{property.bathrooms}</div>
              <div className="text-sm text-gray-600">Bathrooms</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Square className="w-6 h-6 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{property.area}</div>
              <div className="text-sm text-gray-600">sq ft</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900 capitalize">{property.type}</div>
              <div className="text-sm text-gray-600">Type</div>
            </div>
          </div>

          {/* Property Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
              <p className="text-gray-600 leading-relaxed">
                {property.description}
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Amenities</h3>
              <div className="space-y-2">
                {property.amenities.map((amenity, index) => (
                  <div key={index} className="flex items-center text-gray-600">
                    <Check className="w-4 h-4 text-green-500 mr-2" />
                    {amenity}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Property Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Property Information */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Information</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Property Type:</span>
              <span className="font-medium capitalize">{property.type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Bedrooms:</span>
              <span className="font-medium">{property.bedrooms}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Bathrooms:</span>
              <span className="font-medium">{property.bathrooms}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Square Footage:</span>
              <span className="font-medium">{property.area} sq ft</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Monthly Rent:</span>
              <span className="font-medium text-primary-600">${property.price.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Availability:</span>
              <span className={`font-medium ${property.available ? 'text-green-600' : 'text-red-600'}`}>
                {property.available ? 'Available' : 'Not Available'}
              </span>
            </div>
          </div>
        </div>

        {/* Source Information */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Source Information</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Source:</span>
              <span className="font-medium">{property.source}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Scraped:</span>
              <span className="font-medium">
                {property.scrapedAt.toLocaleDateString()} at {property.scrapedAt.toLocaleTimeString()}
              </span>
            </div>
            <div className="pt-4">
              <a
                href={property.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View Original Listing
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Interested in this property?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 transition-colors">
            Contact Landlord
          </button>
          <button className="bg-white text-primary-600 border border-primary-600 py-3 px-6 rounded-lg font-medium hover:bg-primary-50 transition-colors">
            Schedule Viewing
          </button>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetail; 