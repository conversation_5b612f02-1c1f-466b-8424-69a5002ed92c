import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, MapPin, DollarSign, Bed, Bath, Square } from 'lucide-react';
import { useProperty } from '../context/PropertyContext';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useProperty();

  const propertyTypes = [
    { type: 'studio', label: 'Studio', icon: '🏠' },
    { type: 'apartment', label: 'Apartment', icon: '🏢' },
    { type: 'villa', label: 'Villa', icon: '🏡' },
    { type: 'house', label: 'House', icon: '🏘️' },
    { type: 'condo', label: 'Condo', icon: '🏬' },
  ];

  const handleSearch = () => {
    navigate('/properties');
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Find Your Perfect Rental Property
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Discover available studios, apartments, villas, and more with our comprehensive property listings
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleSearch}
            className="bg-primary-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Search className="w-5 h-5" />
            <span>Browse Properties</span>
          </button>
        </div>
      </div>

      {/* Property Types */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Property Types Available
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {propertyTypes.map(({ type, label, icon }) => (
            <div
              key={type}
              className="bg-white p-6 rounded-lg shadow-sm border text-center hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => {
                dispatch({ type: 'UPDATE_FILTERS', payload: { type } });
                navigate('/properties');
              }}
            >
              <div className="text-3xl mb-2">{icon}</div>
              <div className="font-medium text-gray-900 capitalize">{label}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="mb-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-primary-600 mb-2">
              {state.properties.length}
            </div>
            <div className="text-gray-600">Total Properties</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {state.properties.filter(p => p.available).length}
            </div>
            <div className="text-gray-600">Available Now</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {state.properties.length > 0 
                ? Math.round(state.properties.reduce((sum, p) => sum + p.price, 0) / state.properties.length)
                : 0
              }
            </div>
            <div className="text-gray-600">Avg. Price</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {new Set(state.properties.map(p => p.source)).size}
            </div>
            <div className="text-gray-600">Sources</div>
          </div>
        </div>
      </div>

      {/* Recent Properties */}
      {state.properties.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Recently Added Properties
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {state.properties.slice(0, 6).map((property) => (
              <div
                key={property.id}
                className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow cursor-pointer property-card"
                onClick={() => navigate(`/property/${property.id}`)}
              >
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  {property.imageUrl ? (
                    <img
                      src={property.imageUrl}
                      alt={property.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-gray-400 text-4xl">🏠</div>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 truncate">
                    {property.title}
                  </h3>
                  <div className="flex items-center text-gray-600 mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span className="text-sm">{property.location}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-primary-600 font-semibold">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {property.price.toLocaleString()}
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Bed className="w-4 h-4 mr-1" />
                        {property.bedrooms}
                      </div>
                      <div className="flex items-center">
                        <Bath className="w-4 h-4 mr-1" />
                        {property.bathrooms}
                      </div>
                      <div className="flex items-center">
                        <Square className="w-4 h-4 mr-1" />
                        {property.area}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Home; 