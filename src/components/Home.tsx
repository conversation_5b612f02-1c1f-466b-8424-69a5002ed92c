import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, MapPin, DollarSign, Bed, Bath, Square, Loader2, RefreshCw } from 'lucide-react';
import { useProperty } from '../context/PropertyContext';
import scraperService from '../services/scraperService';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useProperty();
  const [isLoading, setIsLoading] = useState(false);
  const [scrapingStatus, setScrapingStatus] = useState<string>('');

  const propertyTypes = [
    { type: 'studio', label: 'Studio', icon: '🏠' },
    { type: 'apartment', label: 'Apartment', icon: '🏢' },
    { type: 'villa', label: 'Villa', icon: '🏡' },
    { type: 'house', label: 'House', icon: '🏘️' },
    { type: 'condo', label: 'Condo', icon: '🏬' },
  ];

  const handleSearch = () => {
    navigate('/properties');
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    setScrapingStatus('🔄 Refreshing properties from UAE websites...');

    // Clear existing properties
    dispatch({ type: 'CLEAR_PROPERTIES' });

    try {
      const results = await scraperService.scrapeProperties();

      let totalProperties = 0;
      results.forEach(result => {
        if (result.success) {
          result.properties.forEach(property => {
            dispatch({ type: 'ADD_PROPERTY', payload: property });
            totalProperties++;
          });
        }
      });

      setScrapingStatus(`✅ Refreshed! Found ${totalProperties} properties from ${results.length} sources`);
      setTimeout(() => setScrapingStatus(''), 3000);

    } catch (error) {
      setScrapingStatus('⚠️ Refresh failed - using cached data');
      setTimeout(() => setScrapingStatus(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-scrape properties when component mounts
  useEffect(() => {
    const autoScrape = async () => {
      if (state.properties.length === 0 && !isLoading) {
        setIsLoading(true);
        setScrapingStatus('🔍 Searching UAE property websites...');

        try {
          console.log('🚀 Starting automatic property scraping...');
          const results = await scraperService.scrapeProperties();

          let totalProperties = 0;
          results.forEach(result => {
            if (result.success) {
              setScrapingStatus(`✅ Found ${result.properties.length} properties from ${result.source}`);
              result.properties.forEach(property => {
                dispatch({ type: 'ADD_PROPERTY', payload: property });
                totalProperties++;
              });
            }
          });

          if (totalProperties > 0) {
            setScrapingStatus(`🎉 Successfully loaded ${totalProperties} properties from ${results.length} sources!`);
          } else {
            setScrapingStatus('📊 Loaded realistic UAE property data for demonstration');
          }

          // Clear status after 3 seconds
          setTimeout(() => setScrapingStatus(''), 3000);

        } catch (error) {
          console.error('Auto-scraping error:', error);
          setScrapingStatus('⚠️ Using demo data - deploy to server for live scraping');
          setTimeout(() => setScrapingStatus(''), 3000);
        } finally {
          setIsLoading(false);
        }
      }
    };

    autoScrape();
  }, [state.properties.length, dispatch, isLoading]);

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Find Your Perfect Rental Property in UAE
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Discover available studios, apartments, villas, and more from top UAE property websites
        </p>

        {/* Loading/Status Indicator */}
        {(isLoading || scrapingStatus) && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-3">
              {isLoading && <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />}
              <span className="text-blue-800 font-medium">
                {scrapingStatus || 'Loading properties...'}
              </span>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleSearch}
            className="bg-primary-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Search className="w-5 h-5" />
            <span>Browse Properties</span>
          </button>
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-green-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            <span>{isLoading ? 'Refreshing...' : 'Refresh Properties'}</span>
          </button>
        </div>
      </div>

      {/* Property Types */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
          Property Types Available
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {propertyTypes.map(({ type, label, icon }) => (
            <div
              key={type}
              className="bg-white p-6 rounded-lg shadow-sm border text-center hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => {
                dispatch({ type: 'UPDATE_FILTERS', payload: { type } });
                navigate('/properties');
              }}
            >
              <div className="text-3xl mb-2">{icon}</div>
              <div className="font-medium text-gray-900 capitalize">{label}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="mb-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-primary-600 mb-2">
              {state.properties.length}
            </div>
            <div className="text-gray-600">Total Properties</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {state.properties.filter(p => p.available).length}
            </div>
            <div className="text-gray-600">Available Now</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {state.properties.length > 0 
                ? Math.round(state.properties.reduce((sum, p) => sum + p.price, 0) / state.properties.length)
                : 0
              }
            </div>
            <div className="text-gray-600">Avg. Price</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {new Set(state.properties.map(p => p.source)).size}
            </div>
            <div className="text-gray-600">Sources</div>
          </div>
        </div>
      </div>

      {/* Recent Properties */}
      {state.properties.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Recently Added Properties
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {state.properties.slice(0, 6).map((property) => (
              <div
                key={property.id}
                className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow cursor-pointer property-card"
                onClick={() => navigate(`/property/${property.id}`)}
              >
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  {property.imageUrl ? (
                    <img
                      src={property.imageUrl}
                      alt={property.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-gray-400 text-4xl">🏠</div>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 truncate">
                    {property.title}
                  </h3>
                  <div className="flex items-center text-gray-600 mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span className="text-sm">{property.location}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-primary-600 font-semibold">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {property.price.toLocaleString()}
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Bed className="w-4 h-4 mr-1" />
                        {property.bedrooms}
                      </div>
                      <div className="flex items-center">
                        <Bath className="w-4 h-4 mr-1" />
                        {property.bathrooms}
                      </div>
                      <div className="flex items-center">
                        <Square className="w-4 h-4 mr-1" />
                        {property.area}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Home; 