import { Property } from '../context/PropertyContext';

export interface ScrapingResult {
  success: boolean;
  properties: Property[];
  source: string;
  error?: string;
  note?: string;
}

class ScraperService {
  // Load properties from pre-scraped JSON data
  public async loadPropertiesFromData(): Promise<ScrapingResult[]> {
    try {
      console.log('📂 Loading properties from data file...');

      // Try to import the properties data
      const propertiesData = await import('../data/properties.json');
      const rawProperties = propertiesData.default || propertiesData;

      // Convert raw data to Property objects with proper types
      const properties: Property[] = rawProperties.map((prop: any) => ({
        ...prop,
        scrapedAt: new Date(prop.scrapedAt),
        type: prop.type as Property['type'] // Ensure type is properly cast
      }));

      if (!Array.isArray(properties) || properties.length === 0) {
        console.log('⚠️ No properties found in data file, generating sample data...');
        return this.generateSampleData();
      }

      // Group properties by source
      const sourceGroups: { [key: string]: Property[] } = {};
      properties.forEach(property => {
        if (!sourceGroups[property.source]) {
          sourceGroups[property.source] = [];
        }
        sourceGroups[property.source].push(property);
      });

      // Create results for each source
      const results: ScrapingResult[] = Object.entries(sourceGroups).map(([source, props]) => ({
        success: true,
        properties: props,
        source: source,
        note: `Loaded ${props.length} properties from data file`
      }));

      console.log(`✅ Loaded ${properties.length} properties from ${results.length} sources`);
      return results;

    } catch (error) {
      console.log('⚠️ Could not load properties data file, generating sample data...');
      return this.generateSampleData();
    }
  }

  // Main method to get properties (loads from data file)
  public async scrapeProperties(): Promise<ScrapingResult[]> {
    console.log('🔍 Loading UAE property data...');
    return this.loadPropertiesFromData();
  }

  // Generate sample data when no data file is available
  private async generateSampleData(): Promise<ScrapingResult[]> {
    console.log('🎭 Generating sample UAE property data...');

    const sampleProperties: Property[] = [
      {
        id: 'sample-1',
        title: 'Luxury Studio in Dubai Marina',
        type: 'studio',
        price: 45000,
        location: 'Dubai Marina, Dubai, UAE',
        bedrooms: 0,
        bathrooms: 1,
        area: 450,
        description: 'Beautiful studio apartment in the heart of Dubai Marina with stunning views.',
        imageUrl: 'https://picsum.photos/400/300?random=1',
        amenities: ['Swimming Pool', 'Gym', 'Parking', 'Security'],
        available: true,
        source: 'Property Finder UAE',
        url: 'https://www.propertyfinder.ae/sample-1',
        scrapedAt: new Date()
      },
      {
        id: 'sample-2',
        title: '1 Bedroom Apartment in Downtown Dubai',
        type: 'apartment',
        price: 65000,
        location: 'Downtown Dubai, Dubai, UAE',
        bedrooms: 1,
        bathrooms: 1,
        area: 750,
        description: 'Modern 1-bedroom apartment in Downtown Dubai with city views.',
        imageUrl: 'https://picsum.photos/400/300?random=2',
        amenities: ['Swimming Pool', 'Gym', 'Concierge', 'Central AC'],
        available: true,
        source: 'Bayut Dubai',
        url: 'https://www.bayut.com/sample-2',
        scrapedAt: new Date()
      },
      {
        id: 'sample-3',
        title: '2 Bedroom Villa in Al Reem Island',
        type: 'villa',
        price: 120000,
        location: 'Al Reem Island, Abu Dhabi, UAE',
        bedrooms: 2,
        bathrooms: 2,
        area: 1200,
        description: 'Spacious villa in Al Reem Island with garden and parking.',
        imageUrl: 'https://picsum.photos/400/300?random=3',
        amenities: ['Garden', 'Parking', 'Security', 'Balcony'],
        available: true,
        source: 'Bayut Abu Dhabi',
        url: 'https://www.bayut.com/sample-3',
        scrapedAt: new Date()
      }
    ];

    return [{
      success: true,
      properties: sampleProperties,
      source: 'Sample Data',
      note: 'Using sample data. Run "npm run generate-data" to create realistic UAE property data.'
    }];
  }
}

const scraperService = new ScraperService();
export default scraperService;