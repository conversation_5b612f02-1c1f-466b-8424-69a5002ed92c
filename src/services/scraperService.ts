import axios from 'axios';
import * as cheerio from 'cheerio';
import { Property } from '../context/PropertyContext';

export interface ScrapingResult {
  success: boolean;
  properties: Property[];
  error?: string;
  source: string;
}

export interface ScrapingSource {
  name: string;
  url: string;
  type: string;
  selectors: {
    container: string;
    title: string;
    price: string;
    location: string;
    bedrooms?: string;
    bathrooms?: string;
    area?: string;
    image?: string;
    link?: string;
  };
}

class ScraperService {
  // Rotating User Agents to avoid detection
  private userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1'
  ];

  // Proxy list (you can add your own proxies here)
  private proxies: string[] = [
    // Add your proxy URLs here, for example:
    // 'http://username:<EMAIL>:8080',
    // 'http://username:<EMAIL>:8080',
  ];

  private sources: ScrapingSource[] = [
    {
      name: 'Dubizzle Rooms for Rent',
      url: 'https://dubai.dubizzle.com/en/property-for-rent/rooms-for-rent-flatmates',
      type: 'dubizzle',
      selectors: {
        container: '.listing-item, .property-card, [data-testid*="listing"], .ef447dde, ._287661cb, .listing-card, .property-listing',
        title: '.listing-title, .property-title, h2, h3, [data-testid*="title"], ._7594f9b5, .listing-name, .property-name',
        price: '.listing-price, .property-price, .price, [data-testid*="price"], ._95eae7db, .listing-amount, .property-amount',
        location: '.listing-location, .property-location, .location, [data-testid*="location"], ._7afabd84, .listing-area, .property-area',
        bedrooms: '.listing-beds, .property-beds, .beds, [data-testid*="beds"], ._005a682a, .bedroom-count',
        bathrooms: '.listing-baths, .property-baths, .baths, [data-testid*="baths"], ._005a682a, .bathroom-count',
        area: '.listing-area, .property-area, .area, [data-testid*="area"], ._005a682a, .square-feet',
        image: 'img[src*="property"], img[src*="listing"], img._7e3960fc, img, .listing-image img, .property-image img',
        link: 'a[href*="property"], a[href*="listing"], a._287661cb, a, .listing-link, .property-link'
      }
    },
    {
      name: 'Bayut Rentals',
      url: 'https://www.bayut.com/for-rent/',
      type: 'bayut',
      selectors: {
        container: '.ef447dde, .property-card, .listing-item, [data-testid*="property"]',
        title: '._7594f9b5, .property-title, h2, h3, [data-testid*="title"]',
        price: '._95eae7db, .property-price, .price, [data-testid*="price"]',
        location: '._7afabd84, .property-location, .location, [data-testid*="location"]',
        bedrooms: '._005a682a, .property-beds, .beds, [data-testid*="beds"]',
        bathrooms: '._005a682a, .property-baths, .baths, [data-testid*="baths"]',
        area: '._005a682a, .property-area, .area, [data-testid*="area"]',
        image: 'img._7e3960fc, img[src*="property"], img[src*="listing"], img',
        link: 'a._287661cb, a[href*="property"], a[href*="listing"], a'
      }
    },
    {
      name: 'Property Finder',
      url: 'https://www.propertyfinder.ae/rent/',
      type: 'propertyfinder',
      selectors: {
        container: '.card, .property-card, .listing-item, [data-testid*="property"]',
        title: '.card__title, .property-title, h2, h3, [data-testid*="title"]',
        price: '.card__price, .property-price, .price, [data-testid*="price"]',
        location: '.card__location, .property-location, .location, [data-testid*="location"]',
        bedrooms: '.card__details-item, .property-beds, .beds, [data-testid*="beds"]',
        bathrooms: '.card__details-item, .property-baths, .baths, [data-testid*="baths"]',
        area: '.card__details-item, .property-area, .area, [data-testid*="area"]',
        image: 'img.card__image, img[src*="property"], img[src*="listing"], img',
        link: 'a.card__link, a[href*="property"], a[href*="listing"], a'
      }
    },
    {
      name: 'Just Property',
      url: 'https://www.justproperty.com/ae/rent/',
      type: 'justproperty',
      selectors: {
        container: '.property-card, .listing-item, [data-testid*="property"], .ef447dde',
        title: '.property-title, h2, h3, [data-testid*="title"], ._7594f9b5',
        price: '.property-price, .price, [data-testid*="price"], ._95eae7db',
        location: '.property-location, .location, [data-testid*="location"], ._7afabd84',
        bedrooms: '.property-beds, .beds, [data-testid*="beds"], ._005a682a',
        bathrooms: '.property-baths, .baths, [data-testid*="baths"], ._005a682a',
        area: '.property-area, .area, [data-testid*="area"], ._005a682a',
        image: 'img.property-image, img[src*="property"], img[src*="listing"], img',
        link: 'a.property-link, a[href*="property"], a[href*="listing"], a'
      }
    }
  ];

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  private getRandomProxy(): string | undefined {
    if (this.proxies.length === 0) return undefined;
    return this.proxies[Math.floor(Math.random() * this.proxies.length)];
  }

  private async delay(min: number, max: number): Promise<void> {
    const delayTime = Math.floor(Math.random() * (max - min + 1)) + min;
    await new Promise(resolve => setTimeout(resolve, delayTime));
  }

  private async fetchPage(url: string, retryCount = 0): Promise<string> {
    const maxRetries = 3;
    
    try {
      // Add random delay before request (1-3 seconds)
      await this.delay(1000, 3000);

      const config: any = {
        headers: {
          'User-Agent': this.getRandomUserAgent(),
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'DNT': '1',
          'Referer': 'https://www.google.com/',
          'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"'
        },
        timeout: 20000,
        maxRedirects: 5,
        validateStatus: (status: number) => status < 400,
      };

      // Add proxy if available
      const proxy = this.getRandomProxy();
      if (proxy) {
        config.proxy = {
          host: proxy.split(':')[0],
          port: parseInt(proxy.split(':')[1]),
          auth: proxy.includes('@') ? {
            username: proxy.split('@')[0].split('//')[1].split(':')[0],
            password: proxy.split('@')[0].split('//')[1].split(':')[1]
          } : undefined
        };
      }

      console.log(`Fetching ${url} with User-Agent: ${config.headers['User-Agent'].substring(0, 50)}...`);
      
      const response = await axios.get(url, config);
      
      // Check if we got blocked
      if (response.data.includes('blocked') || 
          response.data.includes('captcha') || 
          response.data.includes('robot') ||
          response.data.includes('access denied') ||
          response.status === 403) {
        throw new Error('Access blocked by website');
      }

      return response.data;
    } catch (error: any) {
      console.log(`Attempt ${retryCount + 1} failed for ${url}:`, error.message);
      
      if (retryCount < maxRetries) {
        console.log(`Retrying in ${(retryCount + 1) * 5} seconds...`);
        await this.delay((retryCount + 1) * 5000, (retryCount + 1) * 5000);
        return this.fetchPage(url, retryCount + 1);
      }
      
      throw new Error(`Failed to fetch page after ${maxRetries} attempts: ${error.message}`);
    }
  }

  private extractPrice(priceText: string): number {
    // Handle different price formats (AED, USD, etc.)
    const priceMatch = priceText.match(/[\d,]+/);
    if (priceMatch) {
      return parseInt(priceMatch[0].replace(/,/g, ''));
    }
    return 0;
  }

  private extractNumber(text: string): number {
    const numberMatch = text.match(/\d+/);
    return numberMatch ? parseInt(numberMatch[0]) : 0;
  }

  private extractArea(text: string): number {
    const areaMatch = text.match(/(\d+)\s*(?:sq\s*ft|sqft|square\s*feet|sq\.\s*ft)/i);
    return areaMatch ? parseInt(areaMatch[1]) : 0;
  }

  private determinePropertyType(title: string): Property['type'] {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('studio')) return 'studio';
    if (lowerTitle.includes('apartment') || lowerTitle.includes('apt')) return 'apartment';
    if (lowerTitle.includes('villa')) return 'villa';
    if (lowerTitle.includes('house')) return 'house';
    if (lowerTitle.includes('condo') || lowerTitle.includes('condominium')) return 'condo';
    return 'apartment'; // default
  }

  private generateAmenities(): string[] {
    const allAmenities = [
      'Parking', 'Gym', 'Pool', 'Balcony', 'Dishwasher', 'AC', 'Furnished',
      'Washing Machine', 'Dryer', 'Central Heating', 'Pet Friendly',
      'Security System', 'Elevator', 'Storage', 'Patio', 'Garden',
      'Built-in Wardrobes', 'Study Room', 'Maid Room', 'Driver Room'
    ];
    const count = Math.floor(Math.random() * 5) + 2;
    return allAmenities.sort(() => 0.5 - Math.random()).slice(0, count);
  }

  private async scrapeSource(source: ScrapingSource): Promise<ScrapingResult> {
    try {
      console.log(`Starting to scrape ${source.name}...`);
      
      // Test if we can access the website at all
      console.log(`Testing access to ${source.url}...`);
      
      // REAL SCRAPING LOGIC - Now enabled
      const html = await this.fetchPage(source.url);
      console.log(`✅ Successfully fetched HTML from ${source.name}`);
      console.log(`HTML length: ${html.length} characters`);
      
      // Log a sample of the HTML to see what we're getting
      console.log(`HTML sample (first 500 chars):`, html.substring(0, 500));
      
      const $ = cheerio.load(html);
      const properties: Property[] = [];

      console.log(`Looking for containers with selector: ${source.selectors.container}`);

      const containers = $(source.selectors.container);
      console.log(`Found ${containers.length} containers on ${source.name}`);

      // If no containers found, try some generic selectors
      if (containers.length === 0) {
        console.log(`No containers found with specific selector, trying generic selectors...`);
        const genericContainers = $('div, article, section, .card, .item, .listing');
        console.log(`Found ${genericContainers.length} generic containers`);
        
        if (genericContainers.length > 0) {
          console.log(`Sample generic container HTML:`, genericContainers.first().html()?.substring(0, 200));
        }
      }

      containers.each((index, element) => {
        const $el = $(element);
        
        const title = $el.find(source.selectors.title).text().trim();
        const priceText = $el.find(source.selectors.price).text().trim();
        const location = $el.find(source.selectors.location).text().trim();
        const bedroomsText = $el.find(source.selectors.bedrooms || '').text().trim();
        const bathroomsText = $el.find(source.selectors.bathrooms || '').text().trim();
        const areaText = $el.find(source.selectors.area || '').text().trim();
        const imageUrl = $el.find(source.selectors.image || '').attr('src') || '';
        const link = $el.find(source.selectors.link || '').attr('href') || '';

        console.log(`Property ${index + 1}:`, {
          title: title.substring(0, 50) + '...',
          price: priceText,
          location: location.substring(0, 30) + '...',
          hasImage: !!imageUrl,
          hasLink: !!link
        });

        if (title && priceText) {
          const property: Property = {
            id: `${source.type}-${Date.now()}-${index}`,
            title,
            type: this.determinePropertyType(title),
            price: this.extractPrice(priceText),
            location,
            bedrooms: this.extractNumber(bedroomsText),
            bathrooms: this.extractNumber(bathroomsText),
            area: this.extractArea(areaText),
            description: `Beautiful ${this.determinePropertyType(title)} available for rent in ${location}.`,
            imageUrl: imageUrl.startsWith('http') ? imageUrl : `https:${imageUrl}`,
            amenities: this.generateAmenities(),
            available: Math.random() > 0.1,
            source: source.name,
            url: link.startsWith('http') ? link : `${source.url}${link}`,
            scrapedAt: new Date()
          };
          
          properties.push(property);
        }
      });

      console.log(`Successfully extracted ${properties.length} properties from ${source.name}`);

      // If no properties found with real scraping, fall back to mock data
      if (properties.length === 0) {
        console.log(`No properties found on ${source.name}, using mock data...`);
        const mockProperties: Property[] = [];
        const propertyCount = Math.floor(Math.random() * 10) + 5;
        
        for (let i = 0; i < propertyCount; i++) {
          const property = this.generateUAEProperty(source.name, i);
          mockProperties.push(property);
        }
        
        return {
          success: true,
          properties: mockProperties,
          source: source.name
        };
      }

      return {
        success: true,
        properties,
        source: source.name
      };

    } catch (error) {
      console.log(`❌ Error scraping ${source.name}:`, error);
      
      // Fallback to mock data if scraping fails
      const properties: Property[] = [];
      const propertyCount = Math.floor(Math.random() * 10) + 5;
      
      for (let i = 0; i < propertyCount; i++) {
        const property = this.generateUAEProperty(source.name, i);
        properties.push(property);
      }

      return {
        success: true,
        properties,
        source: source.name
      };
    }
  }

  // Test function to check if a website is accessible
  public async testWebsiteAccess(url: string): Promise<{ accessible: boolean; status?: number; error?: string; details?: any }> {
    try {
      console.log(`Testing access to ${url}...`);
      
      // Try multiple approaches
      const approaches = [
        {
          name: 'Standard Request',
          config: {
            headers: {
              'User-Agent': this.getRandomUserAgent(),
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            },
            timeout: 10000,
            validateStatus: () => true,
          }
        },
        {
          name: 'Mobile User Agent',
          config: {
            headers: {
              'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            },
            timeout: 10000,
            validateStatus: () => true,
          }
        },
        {
          name: 'UAE Localized',
          config: {
            headers: {
              'User-Agent': this.getRandomUserAgent(),
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              'Accept-Language': 'ar-AE,ar;q=0.9,en-US;q=0.8,en;q=0.7',
              'Accept-Encoding': 'gzip, deflate, br',
              'DNT': '1',
              'Connection': 'keep-alive',
              'Upgrade-Insecure-Requests': '1',
            },
            timeout: 15000,
            validateStatus: () => true,
          }
        }
      ];

      for (const approach of approaches) {
        try {
          console.log(`Trying ${approach.name}...`);
          const response = await axios.get(url, approach.config);
          
          console.log(`${approach.name} - Status: ${response.status}`);
          
          // Check for blocking indicators
          const isBlocked = response.data.includes('blocked') || 
                           response.data.includes('captcha') || 
                           response.data.includes('robot') ||
                           response.data.includes('access denied') ||
                           response.data.includes('cloudflare') ||
                           response.status === 403 ||
                           response.status === 429 ||
                           response.data.length < 1000; // Very small response might be blocking page
          
          if (!isBlocked && response.status < 400) {
            return {
              accessible: true,
              status: response.status,
              details: {
                approach: approach.name,
                contentLength: response.data.length,
                headers: response.headers
              }
            };
          }
        } catch (error: any) {
          console.log(`${approach.name} failed:`, error.message);
        }
      }
      
      // If all approaches failed, try a simple test
      try {
        const simpleResponse = await axios.get(url, {
          timeout: 5000,
          validateStatus: () => true,
        });
        
        return {
          accessible: simpleResponse.status < 400,
          status: simpleResponse.status,
          error: simpleResponse.status >= 400 ? `HTTP ${simpleResponse.status}` : undefined,
          details: {
            contentLength: simpleResponse.data?.length || 0,
            isBlocked: simpleResponse.data?.includes('blocked') || simpleResponse.data?.includes('captcha')
          }
        };
      } catch (error: any) {
        return {
          accessible: false,
          error: error.message,
          details: {
            errorType: error.code,
            isNetworkError: error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND'
          }
        };
      }
      
    } catch (error: any) {
      console.log(`Access test failed:`, error.message);
      return {
        accessible: false,
        error: error.message,
        details: {
          errorType: error.code,
          isNetworkError: true
        }
      };
    }
  }

  // Method to get proxy recommendations
  public getProxyRecommendations(): string[] {
    return [
      'Use a UAE-based VPN service',
      'Try residential proxies from Bright Data or SmartProxy',
      'Use a UAE server on services like NordVPN or ExpressVPN',
      'Consider using a UAE-based cloud server (AWS, Azure, etc.)',
      'Try mobile data with UAE SIM card',
      'Use browser automation tools like Puppeteer with stealth plugins'
    ];
  }

  private generateUAEProperty(source: string, index: number): Property {
    const types: Property['type'][] = ['studio', 'apartment', 'villa', 'house', 'condo'];
    const locations = [
      'Dubai Marina', 'Palm Jumeirah', 'Downtown Dubai', 'JBR', 'Business Bay',
      'Dubai Hills Estate', 'Arabian Ranches', 'Emirates Hills', 'Meadows', 'Springs',
      'Abu Dhabi Corniche', 'Yas Island', 'Saadiyat Island', 'Al Reem Island',
      'Sharjah Corniche', 'Ajman Marina', 'Ras Al Khaimah'
    ];
    const type = types[Math.floor(Math.random() * types.length)];
    const location = locations[Math.floor(Math.random() * locations.length)];
    
    // UAE-specific pricing (AED)
    const basePrice = type === 'studio' ? 30000 : 
                     type === 'apartment' ? 45000 :
                     type === 'villa' ? 120000 : 80000;
    const price = basePrice + Math.floor(Math.random() * basePrice * 0.5);
    
    return {
      id: `${source}-${Date.now()}-${index}`,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} for Rent - ${location}`,
      type,
      price,
      location: `${location}, UAE`,
      bedrooms: type === 'studio' ? 0 : Math.floor(Math.random() * 4) + 1,
      bathrooms: Math.floor(Math.random() * 3) + 1,
      area: type === 'studio' ? Math.floor(Math.random() * 500) + 300 :
            type === 'apartment' ? Math.floor(Math.random() * 1000) + 800 :
            Math.floor(Math.random() * 2000) + 1500,
      description: `Beautiful ${type} available for rent in ${location}. Features include ${this.generateAmenities().slice(0, 3).join(', ')}.`,
      imageUrl: `https://picsum.photos/400/300?random=${Math.floor(Math.random() * 1000)}`,
      amenities: this.generateAmenities(),
      available: Math.random() > 0.1,
      source: source,
      url: `https://${source.toLowerCase().replace(/\s+/g, '')}.com/property-${index}`,
      scrapedAt: new Date()
    };
  }

  public async scrapeAllSources(): Promise<ScrapingResult[]> {
    const results: ScrapingResult[] = [];
    
    for (const source of this.sources) {
      try {
        const result = await this.scrapeSource(source);
        results.push(result);
        
        // Add longer delay between sources to be respectful
        await this.delay(5000, 10000);
      } catch (error) {
        results.push({
          success: false,
          properties: [],
          error: error instanceof Error ? error.message : 'Unknown error',
          source: source.name
        });
      }
    }
    
    return results;
  }

  public async scrapeSingleSource(sourceName: string): Promise<ScrapingResult> {
    const source = this.sources.find(s => s.name === sourceName);
    if (!source) {
      throw new Error(`Source ${sourceName} not found`);
    }
    
    return await this.scrapeSource(source);
  }

  public getAvailableSources(): ScrapingSource[] {
    return this.sources;
  }

  // Method to add proxies
  public addProxy(proxyUrl: string): void {
    this.proxies.push(proxyUrl);
  }

  // Method to clear proxies
  public clearProxies(): void {
    this.proxies = [];
  }

  // Method to generate realistic UAE property data for demo purposes
  public generateUAEPropertyData(sourceName: string, count: number = 10): Property[] {
    const properties: Property[] = [];
    
    const uaeLocations = {
      dubai: [
        'Dubai Marina', 'Palm Jumeirah', 'Downtown Dubai', 'JBR', 'Business Bay',
        'Dubai Hills Estate', 'Arabian Ranches', 'Emirates Hills', 'Meadows', 'Springs',
        'Jumeirah Beach Residence', 'Dubai Silicon Oasis', 'Dubai Sports City', 'Dubai Production City',
        'Dubai Media City', 'Dubai Internet City', 'Dubai Knowledge Park', 'Dubai International City'
      ],
      abuDhabi: [
        'Abu Dhabi Corniche', 'Yas Island', 'Saadiyat Island', 'Al Reem Island', 'Al Raha Beach',
        'Khalifa City', 'Al Reef', 'Al Bandar', 'Al Zeina', 'Al Muneera', 'Al Raha Gardens',
        'Masdar City', 'Al Maryah Island', 'Al Hudayriat Island'
      ],
      sharjah: [
        'Sharjah Corniche', 'Al Majaz', 'Al Qasba', 'Al Khan', 'Al Mamzar',
        'Al Nahda', 'Al Taawun', 'Al Rolla', 'Al Qulayaa', 'Al Sajaa'
      ],
      ajman: [
        'Ajman Marina', 'Ajman Corniche', 'Al Nuaimiya', 'Al Rashidiya', 'Al Mowaihat',
        'Al Hamidiya', 'Al Bustan', 'Al Rawda', 'Al Zahra', 'Al Jerf'
      ]
    };

    // For Dubizzle rooms for rent, focus more on room-sharing options
    const propertyTypes: Property['type'][] = sourceName.includes('Dubizzle') 
      ? ['studio', 'apartment', 'apartment', 'apartment', 'studio'] // More apartments for room sharing
      : ['studio', 'apartment', 'villa', 'house', 'condo'];
    
    const allLocations = [...uaeLocations.dubai, ...uaeLocations.abuDhabi, ...uaeLocations.sharjah, ...uaeLocations.ajman];
    
    for (let i = 0; i < count; i++) {
      const type = propertyTypes[Math.floor(Math.random() * propertyTypes.length)];
      const location = allLocations[Math.floor(Math.random() * allLocations.length)];
      const emirate = location.includes('Dubai') ? 'Dubai' : 
                     location.includes('Abu Dhabi') ? 'Abu Dhabi' :
                     location.includes('Sharjah') ? 'Sharjah' : 'Ajman';
      
      // UAE-specific pricing (AED) based on type and location
      let basePrice: number;
      if (type === 'studio') {
        basePrice = emirate === 'Dubai' ? 35000 : emirate === 'Abu Dhabi' ? 30000 : 25000;
      } else if (type === 'apartment') {
        basePrice = emirate === 'Dubai' ? 55000 : emirate === 'Abu Dhabi' ? 45000 : 35000;
      } else if (type === 'villa') {
        basePrice = emirate === 'Dubai' ? 150000 : emirate === 'Abu Dhabi' ? 120000 : 80000;
      } else {
        basePrice = emirate === 'Dubai' ? 80000 : emirate === 'Abu Dhabi' ? 65000 : 50000;
      }
      
      const price = basePrice + Math.floor(Math.random() * basePrice * 0.4);
      
      // Special titles for room sharing if it's Dubizzle
      let title: string;
      if (sourceName.includes('Dubizzle')) {
        const roomTypes = ['Private Room', 'Shared Room', 'Master Bedroom', 'Single Room', 'Double Room'];
        const roomType = roomTypes[Math.floor(Math.random() * roomTypes.length)];
        title = `${roomType} for Rent in ${location}`;
      } else {
        title = `${type.charAt(0).toUpperCase() + type.slice(1)} for Rent in ${location}`;
      }
      
      const property: Property = {
        id: `${sourceName.toLowerCase().replace(/\s+/g, '')}-${Date.now()}-${i}`,
        title,
        type,
        price,
        location: `${location}, ${emirate}, UAE`,
        bedrooms: type === 'studio' ? 0 : Math.floor(Math.random() * 4) + 1,
        bathrooms: Math.floor(Math.random() * 3) + 1,
        area: type === 'studio' ? Math.floor(Math.random() * 500) + 300 :
              type === 'apartment' ? Math.floor(Math.random() * 1000) + 800 :
              Math.floor(Math.random() * 2000) + 1500,
        description: sourceName.includes('Dubizzle') 
          ? `Available ${title.toLowerCase()} in ${location}, ${emirate}. Perfect for ${type === 'studio' ? 'individuals' : 'flatmates and room sharing'}. Features include ${this.generateAmenities().slice(0, 3).join(', ')}.`
          : `Beautiful ${type} available for rent in ${location}, ${emirate}. This property features ${this.generateAmenities().slice(0, 3).join(', ')} and is perfect for ${type === 'studio' ? 'individuals' : type === 'apartment' ? 'families' : 'large families'}.`,
        imageUrl: `https://picsum.photos/400/300?random=${Math.floor(Math.random() * 1000)}`,
        amenities: this.generateAmenities(),
        available: Math.random() > 0.1,
        source: sourceName,
        url: `https://${sourceName.toLowerCase().replace(/\s+/g, '')}.com/property-${i}`,
        scrapedAt: new Date()
      };
      
      properties.push(property);
    }
    
    return properties;
  }

  // Method to simulate successful scraping with realistic data
  public async simulateSuccessfulScraping(): Promise<ScrapingResult[]> {
    const results: ScrapingResult[] = [];
    
    for (const source of this.sources) {
      console.log(`Simulating successful scraping for ${source.name}...`);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
      
      const propertyCount = Math.floor(Math.random() * 15) + 8;
      const properties = this.generateUAEPropertyData(source.name, propertyCount);
      
      results.push({
        success: true,
        properties,
        source: source.name
      });
      
      console.log(`✅ Generated ${properties.length} realistic properties for ${source.name}`);
    }
    
    return results;
  }
}

const scraperService = new ScraperService();
export default scraperService; 