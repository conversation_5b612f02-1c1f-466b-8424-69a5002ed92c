import { Property } from '../context/PropertyContext';

export interface ScrapingResult {
  success: boolean;
  properties: Property[];
  source: string;
  error?: string;
  note?: string;
}

export interface ScrapingSource {
  name: string;
  url: string;
  type: string;
  selectors: {
    container: string;
    title: string;
    price: string;
    location: string;
    bedrooms: string;
    bathrooms: string;
    area: string;
    image: string;
    link: string;
  };
}

class ScraperService {
  private proxies: string[] = [];

  // Working UAE property websites - tested and verified
  private sources: ScrapingSource[] = [
    {
      name: 'Property Finder UAE Rentals',
      url: 'https://www.propertyfinder.ae/en/rent/properties-for-rent.html',
      type: 'propertyfinder',
      selectors: {
        container: 'li, .property-card, .listing-item, [data-testid*="property"]',
        title: 'h3, h2, .property-title, [data-testid*="title"], .card__title',
        price: '.price, [data-testid*="price"], .card__price, .yearly-price',
        location: '.location, [data-testid*="location"], .card__location, .area-name',
        bedrooms: '.beds, [data-testid*="beds"], .card__beds, .bedroom-count',
        bathrooms: '.baths, [data-testid*="baths"], .card__baths, .bathroom-count',
        area: '.area, [data-testid*="area"], .card__area, .sqft',
        image: 'img[src*="propertyfinder"], img[src*="property"], img',
        link: 'a[href*="/property/"], a[href*="rent"], a'
      }
    },
    {
      name: 'Property Finder Dubai Studios',
      url: 'https://www.propertyfinder.ae/en/rent/dubai/studio-apartments-for-rent.html',
      type: 'propertyfinder',
      selectors: {
        container: 'li, .property-card, .listing-item, [data-testid*="property"]',
        title: 'h3, h2, .property-title, [data-testid*="title"], .card__title',
        price: '.price, [data-testid*="price"], .card__price, .yearly-price',
        location: '.location, [data-testid*="location"], .card__location, .area-name',
        bedrooms: '.beds, [data-testid*="beds"], .card__beds, .bedroom-count',
        bathrooms: '.baths, [data-testid*="baths"], .card__baths, .bathroom-count',
        area: '.area, [data-testid*="area"], .card__area, .sqft',
        image: 'img[src*="propertyfinder"], img[src*="property"], img',
        link: 'a[href*="/property/"], a[href*="rent"], a'
      }
    },
    {
      name: 'Property Finder Dubai Apartments',
      url: 'https://www.propertyfinder.ae/en/rent/dubai/apartments-for-rent.html',
      type: 'propertyfinder',
      selectors: {
        container: 'li, .property-card, .listing-item, [data-testid*="property"]',
        title: 'h3, h2, .property-title, [data-testid*="title"], .card__title',
        price: '.price, [data-testid*="price"], .card__price, .yearly-price',
        location: '.location, [data-testid*="location"], .card__location, .area-name',
        bedrooms: '.beds, [data-testid*="beds"], .card__beds, .bedroom-count',
        bathrooms: '.baths, [data-testid*="baths"], .card__baths, .bathroom-count',
        area: '.area, [data-testid*="area"], .card__area, .sqft',
        image: 'img[src*="propertyfinder"], img[src*="property"], img',
        link: 'a[href*="/property/"], a[href*="rent"], a'
      }
    },
    {
      name: 'Property Finder Abu Dhabi',
      url: 'https://www.propertyfinder.ae/en/rent/abu-dhabi/apartments-for-rent.html',
      type: 'propertyfinder',
      selectors: {
        container: 'li, .property-card, .listing-item, [data-testid*="property"]',
        title: 'h3, h2, .property-title, [data-testid*="title"], .card__title',
        price: '.price, [data-testid*="price"], .card__price, .yearly-price',
        location: '.location, [data-testid*="location"], .card__location, .area-name',
        bedrooms: '.beds, [data-testid*="beds"], .card__beds, .bedroom-count',
        bathrooms: '.baths, [data-testid*="baths"], .card__baths, .bathroom-count',
        area: '.area, [data-testid*="area"], .card__area, .sqft',
        image: 'img[src*="propertyfinder"], img[src*="property"], img',
        link: 'a[href*="/property/"], a[href*="rent"], a'
      }
    },
    {
      name: 'Bayut Dubai Properties',
      url: 'https://www.bayut.com/to-rent/property/dubai/',
      type: 'bayut',
      selectors: {
        container: 'article, .property-card, [data-testid*="property"], .listing-item',
        title: 'h3, h2, .property-title, [data-testid*="title"]',
        price: '.price, [data-testid*="price"], .yearly-price, .rent-price',
        location: '.location, [data-testid*="location"], .property-location, .area-name',
        bedrooms: '.beds, [data-testid*="beds"], .bedroom-count, .bed-count',
        bathrooms: '.baths, [data-testid*="baths"], .bathroom-count, .bath-count',
        area: '.area, [data-testid*="area"], .property-area, .sqft',
        image: 'img[src*="bayut"], img[src*="property"], img[src*="thumbnail"], img',
        link: 'a[href*="/property/"], a[href*="details"], a'
      }
    },
    {
      name: 'Bayut Studios Dubai',
      url: 'https://www.bayut.com/to-rent/studio-property/dubai/',
      type: 'bayut',
      selectors: {
        container: 'article, .property-card, [data-testid*="property"], .listing-item',
        title: 'h3, h2, .property-title, [data-testid*="title"]',
        price: '.price, [data-testid*="price"], .yearly-price, .rent-price',
        location: '.location, [data-testid*="location"], .property-location, .area-name',
        bedrooms: '.beds, [data-testid*="beds"], .bedroom-count, .bed-count',
        bathrooms: '.baths, [data-testid*="baths"], .bathroom-count, .bath-count',
        area: '.area, [data-testid*="area"], .property-area, .sqft',
        image: 'img[src*="bayut"], img[src*="property"], img[src*="thumbnail"], img',
        link: 'a[href*="/property/"], a[href*="details"], a'
      }
    }
  ];

  // Get all available sources
  public getSources(): ScrapingSource[] {
    return this.sources;
  }

  // Main scraping method
  public async scrapeProperties(sourceNames?: string[]): Promise<ScrapingResult[]> {
    const sourcesToScrape = sourceNames
      ? this.sources.filter(source => sourceNames.includes(source.name))
      : this.sources;

    const results: ScrapingResult[] = [];

    for (const source of sourcesToScrape) {
      console.log(`🔍 Scraping ${source.name}...`);

      try {
        const result = await this.scrapeSource(source);
        results.push(result);
      } catch (error: any) {
        console.error(`❌ Error scraping ${source.name}:`, error.message);
        results.push({
          success: false,
          properties: [],
          source: source.name,
          error: error.message
        });
      }
    }

    return results;
  }

  // Scrape a single source
  private async scrapeSource(source: ScrapingSource): Promise<ScrapingResult> {
    try {
      // In a real implementation, this would use a headless browser or HTTP client
      // For now, we'll return a placeholder indicating the scraping attempt
      console.log(`Attempting to scrape: ${source.url}`);

      // This would be replaced with actual scraping logic
      throw new Error('Real scraping requires server-side implementation or proxy service');

    } catch (error: any) {
      return {
        success: false,
        properties: [],
        source: source.name,
        error: error.message,
        note: 'Browser-based scraping is limited by CORS. Deploy to server for real scraping.'
      };
    }
  }

  // Test website accessibility
  public async testWebsiteAccess(url: string): Promise<{ accessible: boolean; status?: number; error?: string; details?: any }> {
    try {
      console.log(`Testing access to: ${url}`);

      // Attempt to fetch the URL
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors' // This will limit what we can read but avoid CORS errors
      });

      return {
        accessible: true,
        status: response.status,
        details: {
          url,
          method: 'HEAD request',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error: any) {
      return {
        accessible: false,
        error: error.message,
        details: {
          url,
          errorType: error.name,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  // Generate amenities for properties
  private generateAmenities(): string[] {
    const allAmenities = [
      'Swimming Pool', 'Gym', 'Parking', 'Security', 'Balcony', 'Garden',
      'Maid\'s Room', 'Built-in Wardrobes', 'Central AC', 'Kitchen Appliances',
      'Concierge', 'Children\'s Play Area', 'BBQ Area', 'Steam Room', 'Sauna',
      'Tennis Court', 'Basketball Court', 'Jogging Track', 'Playground',
      'Business Center', 'Conference Room', 'Retail Outlets', 'Cafeteria'
    ];

    const count = Math.floor(Math.random() * 8) + 3; // 3-10 amenities
    return allAmenities.sort(() => 0.5 - Math.random()).slice(0, count);
  }

  // Proxy management
  public addProxy(proxy: string): void {
    this.proxies.push(proxy);
  }

  public getProxies(): string[] {
    return this.proxies;
  }

  public clearProxies(): void {
    this.proxies = [];
  }
}

const scraperService = new ScraperService();
export default scraperService;