import React, { createContext, useContext, useReducer, ReactNode } from 'react';

export interface Property {
  id: string;
  title: string;
  type: 'studio' | 'apartment' | 'villa' | 'house' | 'condo';
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  description: string;
  imageUrl: string;
  amenities: string[];
  available: boolean;
  source: string;
  url: string;
  scrapedAt: Date;
}

interface PropertyState {
  properties: Property[];
  loading: boolean;
  error: string | null;
  filters: {
    type: string;
    minPrice: number;
    maxPrice: number;
    location: string;
    bedrooms: number;
  };
}

type PropertyAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PROPERTIES'; payload: Property[] }
  | { type: 'ADD_PROPERTY'; payload: Property }
  | { type: 'UPDATE_FILTERS'; payload: Partial<PropertyState['filters']> }
  | { type: 'CLEAR_PROPERTIES' };

const initialState: PropertyState = {
  properties: [],
  loading: false,
  error: null,
  filters: {
    type: '',
    minPrice: 0,
    maxPrice: 10000,
    location: '',
    bedrooms: 0,
  },
};

const propertyReducer = (state: PropertyState, action: PropertyAction): PropertyState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_PROPERTIES':
      return { ...state, properties: action.payload };
    case 'ADD_PROPERTY':
      return { ...state, properties: [...state.properties, action.payload] };
    case 'UPDATE_FILTERS':
      return { ...state, filters: { ...state.filters, ...action.payload } };
    case 'CLEAR_PROPERTIES':
      return { ...state, properties: [] };
    default:
      return state;
  }
};

interface PropertyContextType {
  state: PropertyState;
  dispatch: React.Dispatch<PropertyAction>;
  getFilteredProperties: () => Property[];
}

const PropertyContext = createContext<PropertyContextType | undefined>(undefined);

export const PropertyProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(propertyReducer, initialState);

  const getFilteredProperties = (): Property[] => {
    return state.properties.filter(property => {
      const { type, minPrice, maxPrice, location, bedrooms } = state.filters;
      
      if (type && property.type !== type) return false;
      if (property.price < minPrice || property.price > maxPrice) return false;
      if (location && !property.location.toLowerCase().includes(location.toLowerCase())) return false;
      if (bedrooms && property.bedrooms < bedrooms) return false;
      
      return true;
    });
  };

  return (
    <PropertyContext.Provider value={{ state, dispatch, getFilteredProperties }}>
      {children}
    </PropertyContext.Provider>
  );
};

export const useProperty = (): PropertyContextType => {
  const context = useContext(PropertyContext);
  if (context === undefined) {
    throw new Error('useProperty must be used within a PropertyProvider');
  }
  return context;
}; 